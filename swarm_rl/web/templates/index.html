<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quadrotor Swarm Visualization - Three.js</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
</head>
<body>
    <div id="container">
        <div id="info-panel">
            <h2>Quadrotor Swarm Visualization</h2>
            <div id="connection-status">
                <span id="status-indicator" class="disconnected"></span>
                <span id="status-text">Disconnected</span>
            </div>
            <div id="stats">
                <div>Episode: <span id="episode">0</span></div>
                <div>Step: <span id="step">0</span></div>
                <div>Active Drones: <span id="drone-count">0</span></div>
                <div id="wall-info" style="display: none;">
                    <div style="color: #8B4513; font-weight: bold;">Wall Active</div>
                    <div>Height: <span id="wall-height">0</span>m</div>
                    <div>Position: <span id="wall-position">0,0,0</span></div>
                </div>
            </div>
            <div id="controls">
                <button id="toggle-trails-btn">Toggle Trails</button>
            </div>
            <div id="camera-controls">
                <h3>Camera View</h3>
                <div id="camera-view-buttons">
                    <button class="camera-view-btn" data-view="global">Global</button>
                </div>
                <div id="camera-actions">
                    <button id="reset-camera-btn">Reset Camera</button>
                </div>
                <div id="camera-info">
                    <small>Mouse wheel: Zoom in/out</small>
                    <small>Left drag: Rotate 360°</small>
                    <small>Right drag: Pan view</small>
                    <small>R: Reset camera</small>
                    <small>Auto-tracks all drones & goals</small>
                </div>
            </div>

        </div>
        <div id="canvas-container">
            <canvas id="three-canvas"></canvas>
        </div>
    </div>

    <script src="js/visualizer.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
