<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wall Jump Drone Swarm Visualization</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: white;
        }
        
        #container {
            display: flex;
            gap: 20px;
        }
        
        #visualization {
            flex: 1;
            background-color: #000;
            border: 2px solid #333;
            border-radius: 8px;
        }
        
        #info-panel {
            width: 300px;
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            height: fit-content;
        }
        
        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #333;
            border-radius: 5px;
        }
        
        .info-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .drone-info {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #444;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .wall-info {
            background-color: #8B4513;
            color: white;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        
        #status {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .error {
            color: #f44336;
        }
        
        .warning {
            color: #ff9800;
        }
    </style>
</head>
<body>
    <h1>🚁 Wall Jump Drone Swarm Visualization</h1>
    <p>Real-time visualization of drone swarm learning to jump over dynamic walls</p>
    
    <div id="container">
        <canvas id="visualization" width="800" height="600"></canvas>
        
        <div id="info-panel">
            <div class="info-section">
                <h3>Connection Status</h3>
                <div id="status">Connecting...</div>
                <div id="episode-info">Episode: -, Step: -</div>
            </div>
            
            <div class="info-section">
                <h3>Wall Information</h3>
                <div id="wall-info">No wall data</div>
            </div>
            
            <div class="info-section">
                <h3>Drone States</h3>
                <div id="drone-states">No drone data</div>
            </div>
            
            <div class="info-section">
                <h3>Performance</h3>
                <div id="performance">
                    <div>Total Reward: <span id="total-reward">0</span></div>
                    <div>Collisions: <span id="collisions">0</span></div>
                    <div>Success Rate: <span id="success-rate">0%</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection
        const ws = new WebSocket('ws://localhost:8765');
        const canvas = document.getElementById('visualization');
        const ctx = canvas.getContext('2d');
        
        // Visualization state
        let drones = [];
        let wall = null;
        let episode = 0;
        let step = 0;
        let totalReward = 0;
        let collisions = 0;
        let successfulEpisodes = 0;
        let totalEpisodes = 0;
        
        // Visualization settings
        const SCALE = 40; // pixels per meter
        const ORIGIN_X = canvas.width / 2;
        const ORIGIN_Y = canvas.height / 2;
        
        // Colors
        const DRONE_COLOR = '#00ff00';
        const GOAL_COLOR = '#ffff00';
        const WALL_COLOR = '#8B4513';
        const TRAIL_COLOR = '#004400';
        
        // Drone trails
        const droneTrails = new Map();
        const MAX_TRAIL_LENGTH = 50;
        
        ws.onopen = function(event) {
            document.getElementById('status').textContent = 'Connected';
            document.getElementById('status').className = '';
        };
        
        ws.onclose = function(event) {
            document.getElementById('status').textContent = 'Disconnected';
            document.getElementById('status').className = 'error';
        };
        
        ws.onerror = function(error) {
            document.getElementById('status').textContent = 'Connection Error';
            document.getElementById('status').className = 'error';
        };
        
        ws.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                
                if (data.type === 'drone_state') {
                    updateDroneState(data);
                    updateInfoPanel(data);
                    render();
                }
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        };
        
        function updateDroneState(data) {
            const droneState = data.drone_state;
            
            // Update or add drone
            const existingDroneIndex = drones.findIndex(d => d.id === droneState.agent_id);
            if (existingDroneIndex >= 0) {
                drones[existingDroneIndex] = droneState;
            } else {
                drones.push(droneState);
            }
            
            // Update trail
            const droneId = droneState.agent_id;
            if (!droneTrails.has(droneId)) {
                droneTrails.set(droneId, []);
            }
            
            const trail = droneTrails.get(droneId);
            trail.push({
                x: droneState.position.x,
                y: droneState.position.y,
                z: droneState.position.z
            });
            
            if (trail.length > MAX_TRAIL_LENGTH) {
                trail.shift();
            }
            
            // Update wall if exists
            if (droneState.wall && droneState.wall.exists) {
                wall = droneState.wall;
            }
            
            // Update episode/step info
            episode = data.episode;
            step = data.step;
            
            // Update performance metrics
            if (data.reward) {
                totalReward += data.reward;
            }
            
            if (data.done && episode > totalEpisodes) {
                totalEpisodes = episode;
                // Simple success detection: if drone reached near goal
                const distanceToGoal = Math.sqrt(
                    Math.pow(droneState.position.x - droneState.goal.x, 2) +
                    Math.pow(droneState.position.y - droneState.goal.y, 2) +
                    Math.pow(droneState.position.z - droneState.goal.z, 2)
                );
                if (distanceToGoal < 1.0) {
                    successfulEpisodes++;
                }
            }
        }
        
        function updateInfoPanel(data) {
            // Episode info
            document.getElementById('episode-info').textContent = `Episode: ${episode}, Step: ${step}`;
            
            // Wall info
            if (wall && wall.exists) {
                document.getElementById('wall-info').innerHTML = `
                    <div class="wall-info">
                        <strong>Active Wall</strong><br>
                        Position: (${wall.position.x.toFixed(1)}, ${wall.position.y.toFixed(1)}, ${wall.position.z.toFixed(1)})<br>
                        Height: ${wall.height.toFixed(1)}m<br>
                        Width: ${wall.width.toFixed(1)}m<br>
                        Thickness: ${wall.thickness.toFixed(1)}m
                    </div>
                `;
            } else {
                document.getElementById('wall-info').textContent = 'No wall active';
            }
            
            // Drone states
            let droneStatesHtml = '';
            drones.forEach(drone => {
                const speed = Math.sqrt(
                    drone.velocity.vx * drone.velocity.vx +
                    drone.velocity.vy * drone.velocity.vy +
                    drone.velocity.vz * drone.velocity.vz
                ).toFixed(1);
                
                const distanceToGoal = Math.sqrt(
                    Math.pow(drone.position.x - drone.goal.x, 2) +
                    Math.pow(drone.position.y - drone.goal.y, 2) +
                    Math.pow(drone.position.z - drone.goal.z, 2)
                ).toFixed(1);
                
                droneStatesHtml += `
                    <div class="drone-info">
                        <strong>Drone ${drone.agent_id}</strong><br>
                        Pos: (${drone.position.x.toFixed(1)}, ${drone.position.y.toFixed(1)}, ${drone.position.z.toFixed(1)})<br>
                        Speed: ${speed} m/s<br>
                        Goal Distance: ${distanceToGoal}m
                    </div>
                `;
            });
            document.getElementById('drone-states').innerHTML = droneStatesHtml;
            
            // Performance
            document.getElementById('total-reward').textContent = totalReward.toFixed(2);
            document.getElementById('collisions').textContent = collisions;
            const successRate = totalEpisodes > 0 ? (successfulEpisodes / totalEpisodes * 100).toFixed(1) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
        }
        
        function worldToScreen(x, y, z) {
            return {
                x: ORIGIN_X + x * SCALE,
                y: ORIGIN_Y - y * SCALE, // Flip Y axis
                z: z
            };
        }
        
        function render() {
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid
            drawGrid();
            
            // Draw wall
            if (wall && wall.exists) {
                drawWall();
            }
            
            // Draw drone trails
            drawTrails();
            
            // Draw drones and goals
            drones.forEach(drone => {
                drawGoal(drone.goal);
                drawDrone(drone);
            });
            
            // Draw legend
            drawLegend();
        }
        
        function drawGrid() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            
            // Vertical lines
            for (let x = 0; x < canvas.width; x += SCALE) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            
            // Horizontal lines
            for (let y = 0; y < canvas.height; y += SCALE) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            // Origin
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(ORIGIN_X, 0);
            ctx.lineTo(ORIGIN_X, canvas.height);
            ctx.moveTo(0, ORIGIN_Y);
            ctx.lineTo(canvas.width, ORIGIN_Y);
            ctx.stroke();
        }
        
        function drawWall() {
            const pos = worldToScreen(wall.position.x, wall.position.y, wall.position.z);
            const width = wall.width * SCALE;
            const height = wall.height * SCALE;
            const thickness = wall.thickness * SCALE;
            
            ctx.fillStyle = WALL_COLOR;
            ctx.fillRect(
                pos.x - width/2,
                pos.y - height/2,
                thickness,
                height
            );
            
            // Wall outline
            ctx.strokeStyle = '#654321';
            ctx.lineWidth = 2;
            ctx.strokeRect(
                pos.x - width/2,
                pos.y - height/2,
                thickness,
                height
            );
        }
        
        function drawTrails() {
            droneTrails.forEach((trail, droneId) => {
                if (trail.length < 2) return;
                
                ctx.strokeStyle = TRAIL_COLOR;
                ctx.lineWidth = 1;
                ctx.beginPath();
                
                for (let i = 0; i < trail.length; i++) {
                    const pos = worldToScreen(trail[i].x, trail[i].y, trail[i].z);
                    if (i === 0) {
                        ctx.moveTo(pos.x, pos.y);
                    } else {
                        ctx.lineTo(pos.x, pos.y);
                    }
                }
                ctx.stroke();
            });
        }
        
        function drawDrone(drone) {
            const pos = worldToScreen(drone.position.x, drone.position.y, drone.position.z);
            
            // Drone body
            ctx.fillStyle = DRONE_COLOR;
            ctx.beginPath();
            ctx.arc(pos.x, pos.y, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // Drone ID
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(drone.agent_id.toString(), pos.x, pos.y + 4);
            
            // Velocity vector
            const velScale = 10;
            ctx.strokeStyle = '#00ff88';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(pos.x, pos.y);
            ctx.lineTo(
                pos.x + drone.velocity.vx * velScale,
                pos.y - drone.velocity.vy * velScale
            );
            ctx.stroke();
        }
        
        function drawGoal(goal) {
            const pos = worldToScreen(goal.x, goal.y, goal.z);
            
            ctx.fillStyle = GOAL_COLOR;
            ctx.beginPath();
            ctx.arc(pos.x, pos.y, 6, 0, 2 * Math.PI);
            ctx.fill();
            
            // Goal outline
            ctx.strokeStyle = '#ffff88';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        function drawLegend() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(10, 10, 200, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('Legend:', 20, 30);
            
            // Drone
            ctx.fillStyle = DRONE_COLOR;
            ctx.beginPath();
            ctx.arc(30, 50, 6, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.fillText('Drone', 45, 55);
            
            // Goal
            ctx.fillStyle = GOAL_COLOR;
            ctx.beginPath();
            ctx.arc(30, 70, 6, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.fillText('Goal', 45, 75);
            
            // Wall
            ctx.fillStyle = WALL_COLOR;
            ctx.fillRect(25, 85, 10, 10);
            ctx.fillStyle = 'white';
            ctx.fillText('Wall', 45, 95);
        }
        
        // Initial render
        render();
        
        // Render loop
        setInterval(render, 50); // 20 FPS
    </script>
</body>
</html>
