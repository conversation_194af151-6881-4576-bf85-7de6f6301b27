import numpy as np

from gym_art.quadrotor_multi.scenarios.utils import get_z_value
from gym_art.quadrotor_multi.scenarios.base import QuadrotorScenario


class Scenario_wall_jump(QuadrotorScenario):
    def __init__(self, quads_mode, envs, num_agents, room_dims):
        super().__init__(quads_mode, envs, num_agents, room_dims)
        # Wall spawns every [4.0, 6.0] secs
        duration_time = 5.0
        self.control_step_for_sec = int(duration_time * self.envs[0].control_freq)
        
        # Wall properties
        self.wall_position = None
        self.wall_height = 2.0  # Default wall height
        self.wall_width = 8.0   # Wall width (spans across room)
        self.wall_thickness = 0.5  # Wall thickness
        
        # Wall height range (meters)
        self.min_wall_height = 1.0
        self.max_wall_height = 4.0
        
        # Goal position (behind the wall)
        self.goal_distance_behind_wall = 3.0
        
        # Initialize wall and goal
        self.spawn_new_wall_and_goal()

    def spawn_new_wall_and_goal(self):
        """Spawn a new wall with random height and set goal behind it"""
        box_size = self.envs[0].box
        
        # Random wall height
        self.wall_height = np.random.uniform(self.min_wall_height, self.max_wall_height)
        
        # Wall position (in front of drones, spanning room width)
        wall_x = np.random.uniform(-box_size + 2.0, box_size - 2.0)
        wall_y = np.random.uniform(-box_size + 4.0, box_size - 4.0)  # Leave space for goal behind
        wall_z = self.wall_height / 2.0  # Wall center height
        
        self.wall_position = np.array([wall_x, wall_y, wall_z])
        
        # Set goal behind the wall
        goal_y = wall_y + self.goal_distance_behind_wall
        # Make sure goal is within room bounds
        goal_y = min(goal_y, box_size - 1.0)
        
        goal_z = get_z_value(num_agents=self.num_agents, 
                           num_agents_per_layer=self.num_agents_per_layer,
                           box_size=box_size, 
                           formation=self.formation, 
                           formation_size=self.formation_size)
        
        self.formation_center = np.array([wall_x, goal_y, goal_z])
        
        # Update wall in all environments
        for env in self.envs:
            if hasattr(env, 'set_wall'):
                env.set_wall(self.wall_position, self.wall_height, self.wall_width, self.wall_thickness)

    def update_goals(self):
        """Update goals behind the current wall"""
        # Reset formation and related parameters
        self.update_formation_and_relate_param()

        # Reset goals behind the wall
        self.goals = self.generate_goals(num_agents=self.num_agents, 
                                       formation_center=self.formation_center,
                                       layer_dist=self.layer_dist)
        np.random.shuffle(self.goals)

    def step(self):
        tick = self.envs[0].tick
        if tick % self.control_step_for_sec == 0 and tick > 0:
            # Spawn new wall and goal
            self.spawn_new_wall_and_goal()
            self.update_goals()

            # Update goals to envs
            for i, env in enumerate(self.envs):
                env.goal = self.goals[i]

        return

    def reset(self):
        # Update duration time
        duration_time = np.random.uniform(low=4.0, high=6.0)
        self.control_step_for_sec = int(duration_time * self.envs[0].control_freq)

        # Reset formation, and parameters related to the formation; formation center; goals
        self.standard_reset()
        
        # Spawn initial wall and goal
        self.spawn_new_wall_and_goal()
        self.update_goals()
        
        # Update goals to envs
        for i, env in enumerate(self.envs):
            env.goal = self.goals[i]

    def get_wall_info(self):
        """Get current wall information for visualization"""
        if self.wall_position is not None:
            return {
                'position': self.wall_position.tolist(),
                'height': float(self.wall_height),
                'width': float(self.wall_width),
                'thickness': float(self.wall_thickness)
            }
        return None
