#!/usr/bin/env python3

"""
Simple test script for wall_jump scenario without dependencies
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_argument_parsing():
    """Test if wall_jump is accepted as a valid argument"""
    
    try:
        # Test the argument parsing
        from swarm_rl.env_wrappers.quadrotor_params import add_quadrotors_env_args
        import argparse
        
        parser = argparse.ArgumentParser()
        add_quadrotors_env_args('quadrotor_multi', parser)
        
        # Test wall_jump argument
        args = parser.parse_args(['--quads_mode', 'wall_jump'])
        
        assert args.quads_mode == 'wall_jump', f"Expected 'wall_jump', got '{args.quads_mode}'"
        
        print("✅ wall_jump argument parsing test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Argument parsing test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scenario_import():
    """Test if wall_jump scenario can be imported"""
    
    try:
        from gym_art.quadrotor_multi.scenarios.wall_jump import Scenario_wall_jump
        print("✅ wall_jump scenario import test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Scenario import test FAILED: {e}")
        return False

def test_utils_integration():
    """Test if wall_jump is in utils lists"""
    
    try:
        from gym_art.quadrotor_multi.scenarios.utils import QUADS_MODE_LIST, QUADS_MODE_LIST_SINGLE, QUADS_PARAMS_DICT
        
        assert 'wall_jump' in QUADS_MODE_LIST, "wall_jump not in QUADS_MODE_LIST"
        assert 'wall_jump' in QUADS_MODE_LIST_SINGLE, "wall_jump not in QUADS_MODE_LIST_SINGLE"
        assert 'wall_jump' in QUADS_PARAMS_DICT, "wall_jump not in QUADS_PARAMS_DICT"
        
        print("✅ utils integration test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Utils integration test FAILED: {e}")
        return False

def test_mix_integration():
    """Test if wall_jump can be created through mix system"""
    
    try:
        # This will fail if dependencies are missing, but we can at least test the import
        from gym_art.quadrotor_multi.scenarios.mix import create_scenario
        print("✅ mix integration import test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Mix integration test FAILED: {e}")
        return False

def main():
    print("🧪 Testing wall_jump scenario integration...\n")
    
    tests = [
        ("Argument Parsing", test_argument_parsing),
        ("Scenario Import", test_scenario_import),
        ("Utils Integration", test_utils_integration),
        ("Mix Integration", test_mix_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name} test...")
        if test_func():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! wall_jump scenario is properly integrated.")
        print("\n📝 Next steps:")
        print("1. Install missing dependencies (gymnasium, bezier, etc.)")
        print("2. Run training with: ./train_wall_jump.sh")
        print("3. Use web visualization with enjoy.py")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
